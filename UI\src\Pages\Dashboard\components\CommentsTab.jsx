import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";
import useDashboardStore from "../useDashboardStore";

ChartJS.register(ArcElement, Tooltip, Legend);

export default function CommentsTab() {
    const statusLegend = [
        { name: "Finished", color: "#00C0A9" },
        { name: "Upcoming", color: "#298BED" },
        { name: "Past Due", color: "#E81E1ECC" },
        { name: "Incomplete", color: "#556262" },
    ];

    const { comments, taskStats, loading, error } = useDashboardStore();

    // Calculate chart data from comments
    const getChartData = () => {
        // Count comments by status
        const counts = statusLegend.reduce((acc, status) => {
            acc[status.name] = comments.filter(
                (comment) => comment.status === status.name
            ).length;
            return acc;
        }, {});

        // Return chart data structure
        return {
            labels: statusLegend.map((status) => status.name),
            datasets: [
                {
                    label: "Comments",
                    data: statusLegend.map(
                        (status) => counts[status.name] || 0
                    ),
                    backgroundColor: statusLegend.map((status) => status.color),
                    borderWidth: 0,
                },
            ],
        };
    };

    // Determine if chart should be visible
    const hasChartData = () => {
        return getChartData().datasets[0].data.some((value) => value > 0);
    };

    // Chart options
    const chartOptions = {
        responsive: true,
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                enabled: true,
            },
        },
    };

    if (loading) return <div>Loading...</div>;
    if (error) return <div>Error: {error}</div>;

    return (
        <div className="w-full flex gap-6">
            {/* Comment List */}
            <div className="w-2/3 h-full overflow-hidden rounded-lg gap-4 border-[#E8E7EA] border bg-white p-4 flex flex-col">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold">
                        Comment Priority View
                    </h1>
                </div>

                {/* Comment Cards */}
                <div className="flex flex-col w-full justify-center items-center gap-2">
                    {comments.length > 0 ? (
                        comments.map((comment, index) => (
                            <div
                                key={index}
                                className={`w-full border-l-[3px] rounded-r-lg p-4 flex justify-between bg-[#FAFAFA] ${
                                    comment.status === "Incomplete"
                                        ? "border-l-[#556262]"
                                        : comment.status === "Finished"
                                        ? "border-l-[#00C0A9]"
                                        : comment.status === "Upcoming"
                                        ? "border-l-[#298BED]"
                                        : "border-l-[#E81E1ECC]"
                                }`}
                            >
                                {/* Left Content */}
                                <div className="flex flex-col gap-2">
                                    <div className="flex gap-2 items-center">
                                        <h3 className="font-medium">
                                            {comment.name}
                                        </h3>
                                        <span className="bg-[#00C0A91A] py-0.5 text-[#00C0A9] text-xs rounded-full px-3">
                                            {comment.groupName ||
                                                comment.category}
                                        </span>
                                    </div>
                                    <div className="text-[#525252] text-sm">
                                        Due:{" "}
                                        {new Date(
                                            comment.dueDate
                                        ).toLocaleDateString("en-US", {
                                            year: "numeric",
                                            month: "short",
                                        })}
                                    </div>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="w-full aspect-square max-h-64 flex items-center justify-center">
                            <span className="text-gray-400 text-sm">
                                No comments to display
                            </span>
                        </div>
                    )}
                </div>
            </div>

            {/* Status Breakdown */}
            <div className="w-1/3 rounded-lg border-[#E8E7EA] border bg-white p-8 h-fit flex flex-col gap-4">
                <h1 className="text-2xl font-bold">Comment Status Breakdown</h1>

                {/* Status Chart */}
                <div className="w-full px-10 flex flex-col items-center justify-center">
                    {comments.length > 0 && hasChartData() ? (
                        <div className="w-full relative aspect-square max-h-64">
                            <Doughnut
                                data={getChartData()}
                                options={chartOptions}
                                className="max-h-64"
                            />
                        </div>
                    ) : (
                        <div className="w-full aspect-square max-h-64 flex items-center justify-center">
                            <span className="text-gray-400 text-sm">
                                No comments to display
                            </span>
                        </div>
                    )}

                    {/* Status Legend */}
                    <div className="grid grid-cols-2 w-full mt-6 gap-2">
                        {statusLegend.map((status, index) => {
                            const count =
                                taskStats[status.name.toLowerCase()] || 0;
                            return (
                                <div
                                    key={index}
                                    className="flex gap-3 items-center"
                                >
                                    <span
                                        className="inline-block w-4 h-4 rounded-full"
                                        style={{
                                            backgroundColor: status.color,
                                        }}
                                    />
                                    <span className="text-[#525252]">
                                        {status.name}
                                        <span className="text-xs text-gray-500">
                                            {" "}
                                            ({count})
                                        </span>
                                    </span>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
}
