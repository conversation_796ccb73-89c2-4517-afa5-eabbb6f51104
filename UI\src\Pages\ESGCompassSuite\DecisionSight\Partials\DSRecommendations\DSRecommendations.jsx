import {
  Button,
  Checkbox,
  rem,
  ScrollArea,
  Select,
  Table,
  Textarea,
  TextInput,
  Popover,
  Group,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import Loading from "@/Components/Loading";
import { CiEdit, CiSearch, CiFilter } from "react-icons/ci";
import { IoAddOutline } from "react-icons/io5";
import { MdDelete } from "react-icons/md";
import { RecommendationsDecisionSighStartIcon } from "@/assets/svg/ImageSVG";
import { AiOutlineExport } from "react-icons/ai";
import { useNavigate } from "react-router";
import { data } from "./ESDStructure";
import AiApi from "@/Api/aiApiConfig";
import apiConfig from "@/Api/apiDecisionSightConfig";
import * as XLSX from "xlsx";

const INITIAL_RISK_STATE = {};
const SCOPE_MAPPING = {
  Environment: "env",
  Social: "soc",
  Governance: "gov",
};

const TABLE_HEADERS = [
  { value: "Themes", label: "Themes" },
  { value: "Risk Area", label: "Risk Area" },
  { value: "ESG Metrics", label: "ESG Metrics" },
  { value: "Company Response", label: "Company Response" },
  { value: "AI Recommendation", label: "AI Recommendation" },
];

const TextareaGroup = ({ placeholder, value, onChange, disabled }) => (
  <div className="w-[260px] mx-auto">
    <Textarea
      radius="md"
      placeholder={placeholder}
      value={value || ""}
      onChange={(e) => onChange?.(e.target.value)}
      autosize
      disabled={disabled}
    />
  </div>
);

const ActionIcons = ({ onEdit, onAdd, onDelete, showEdit }) => (
  <div className="flex justify-end gap-2 mb-1">
    {showEdit && (
      <CiEdit
        className="text-primary bg-[#9CCDD2] p-1 rounded-lg cursor-pointer"
        size={25}
        onClick={onEdit}
      />
    )}
    {onAdd && (
      <IoAddOutline
        className="text-primary bg-[#9CCDD2] p-1 rounded-lg cursor-pointer"
        size={25}
        onClick={onAdd}
      />
    )}
    {onDelete && (
      <MdDelete
        className="text-primary bg-[#9CCDD2] p-1 rounded-lg cursor-pointer"
        size={25}
        onClick={onDelete}
      />
    )}
  </div>
);

const ColumnChecklist = ({ options, selected, onChange }) => (
  <div className="p-2 bg-white shadow-md rounded-md">
    {options.map((option) => (
      <Group key={option.value} className="py-1">
        <Checkbox
          checked={selected.includes(option.value)}
          onChange={(event) => {
            const newSelected = event.currentTarget.checked
              ? [...selected, option.value]
              : selected.filter((val) => val !== option.value);
            onChange(newSelected);
          }}
          label={option.label}
          color="#07838F"
        />
      </Group>
    ))}
  </div>
);

export default function DSRecommendations({ isAllSolved, refetch }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [state, setState] = useState({
    tData: data,
    postLoading: false,
    selection: [],
    searchValue: "",
    columnFilter: [],
    selectedScope: "Environment",
    rerender: false,
    selectedRisks: INITIAL_RISK_STATE,
    loadingAi: false,
    isEditingSavedData: false,
    selectedIdsForDelete: [],
    deleteLoading: false,
  });

  const updateState = (updates) =>
    setState((prev) => ({ ...prev, ...updates }));

  const toggleRow = (id, dbId) => {
    updateState({
      selection: state.selection.includes(id)
        ? state.selection.filter((item) => item !== id)
        : [...state.selection, id],
      selectedIdsForDelete: state.selection.includes(id)
        ? state.selectedIdsForDelete.filter((item) => item !== dbId)
        : dbId
        ? [...new Set([...state.selectedIdsForDelete, dbId])] // Ensure unique IDs
        : state.selectedIdsForDelete,
    });
  };

  const toggleAll = () => {
    const allRiskAreaIds =
      state.tData[0]?.themes.flatMap((theme) =>
        theme.risk_area.map((ra) => ra.id)
      ) || [];
    const allDbIds =
      state.tData[0]?.themes
        .flatMap((theme) =>
          state.selectedRisks[theme.name]?.filter((r) => r.id).map((r) => r.id)
        )
        .filter((id) => id) || []; // Filter out undefined/null IDs
    updateState({
      selection:
        state.selection.length === allRiskAreaIds.length ? [] : allRiskAreaIds,
      selectedIdsForDelete:
        state.selection.length === allRiskAreaIds.length ? [] : allDbIds,
    });
  };

  const handleTabs = (tab) => {
    updateState({ selectedScope: tab, rerender: true });
    setTimeout(() => updateState({ rerender: false }), 0.5);
  };

  const handleDeleteSelected = useCallback(async () => {
    if (state.selectedIdsForDelete.length === 0) {
      notifications.show({
        title: "No Selection",
        message: "Please select at least one row to delete",
        color: "yellow",
      });
      return;
    }

    updateState({ deleteLoading: true });
    try {
      await apiConfig.post("/delete_ds_data", state.selectedIdsForDelete);

      const response = await apiConfig.get("/get_ds_data", {
        params: {
          category:
            SCOPE_MAPPING[state.selectedScope] ||
            state.selectedScope.toLowerCase(),
        },
      });

      const savedData = response.data;
      const formattedRisks = {};
      savedData.forEach((item) => {
        if (!formattedRisks[item.theme]) {
          formattedRisks[item.theme] = [];
        }
        formattedRisks[item.theme].push({
          risk_name: item.risk_area,
          metrics: [item.esg_metric],
          company_response: item.response,
          recommendations: item.ai_recommendation,
          readOnly: true,
          id: item.id,
        });
      });

      updateState({
        selectedRisks: formattedRisks,
        selectedIdsForDelete: [],
        selection: [],
        deleteLoading: false,
      });

      notifications.show({
        title: "Success",
        message: "Selected items deleted successfully!",
        color: "green",
      });
    } catch (error) {
      console.error("Error deleting data:", error);
      updateState({ deleteLoading: false });
      notifications.show({
        title: "Error",
        message: "Failed to delete selected items. Please try again.",
        color: "red",
      });
    }
  }, [state.selectedIdsForDelete, state.selectedScope]);

  const handleDeleteRow = async (themeName, index) => {
    const themeRisks = state.selectedRisks[themeName] || [];
    const risk = themeRisks[index];

    // If the row has an ID, delete it from the database
    if (risk.id) {
      try {
        updateState({ deleteLoading: true });

        // Call the delete endpoint with the specific ID
        await apiConfig.post("/delete_ds_data", [risk.id]);

        // Fetch updated data from the database
        const response = await apiConfig.get("/get_ds_data", {
          params: {
            category:
              SCOPE_MAPPING[state.selectedScope] ||
              state.selectedScope.toLowerCase(),
          },
        });

        const savedData = response.data;
        const formattedRisks = {};
        savedData.forEach((item) => {
          if (!formattedRisks[item.theme]) {
            formattedRisks[item.theme] = [];
          }
          formattedRisks[item.theme].push({
            risk_name: item.risk_area,
            metrics: [item.esg_metric],
            company_response: item.response,
            recommendations: item.ai_recommendation,
            readOnly: true,
            id: item.id,
          });
        });

        updateState({
          selectedRisks: formattedRisks,
          deleteLoading: false,
        });

        notifications.show({
          title: "Success",
          message: "Row deleted successfully!",
          color: "green",
        });
      } catch (error) {
        console.error("Error deleting row:", error);
        updateState({ deleteLoading: false });
        notifications.show({
          title: "Error",
          message: "Failed to delete the row. Please try again.",
          color: "red",
        });
      }
    } else {
      // If the row doesn't have an ID (not saved in DB), just remove it from the UI
      if (themeRisks.length <= 1) return; // Prevent deleting the last row if it's not saved
      const updatedRisks = themeRisks.filter((_, i) => i !== index);
      updateState({
        selectedRisks: { ...state.selectedRisks, [themeName]: updatedRisks },
      });
      notifications.show({
        title: "Success",
        message: "Row removed from the table!",
        color: "green",
      });
    }
  };

  useEffect(() => {
    const fetchSavedData = async () => {
      try {
        const response = await apiConfig.get("/get_ds_data", {
          params: {
            category:
              SCOPE_MAPPING[state.selectedScope] ||
              state.selectedScope.toLowerCase(),
          },
        });
        const savedData = response.data;

        const formattedRisks = {};
        savedData.forEach((item) => {
          if (!formattedRisks[item.theme]) {
            formattedRisks[item.theme] = [];
          }
          formattedRisks[item.theme].push({
            risk_name: item.risk_area,
            metrics: [item.esg_metric],
            company_response: item.response,
            recommendations: item.ai_recommendation,
            readOnly: true,
            id: item.id,
          });
        });

        updateState({
          selectedRisks: formattedRisks,
          isEditingSavedData: false,
        });
      } catch (error) {
        console.error("Error fetching saved data:", error);
        updateState({ selectedRisks: INITIAL_RISK_STATE });
      }
    };

    fetchSavedData();
    const filteredData = data.filter(
      (pillar) => pillar.Pillar === state.selectedScope
    );
    updateState({ tData: filteredData });
  }, [state.selectedScope]);

  const handleAddRisk = (themeName) => {
    updateState({
      selectedRisks: {
        ...state.selectedRisks,
        [themeName]: [
          ...(state.selectedRisks[themeName] || []),
          {
            risk_name: "",
            metrics: [],
            company_response: "",
            recommendations: "",
            readOnly: false,
          },
        ],
      },
    });
  };

  const handleEdit = (themeName, index) => {
    const updatedRisks = { ...state.selectedRisks };
    const themeRisks = updatedRisks[themeName] || [];
    const risk = themeRisks[index];
    if (risk.readOnly) {
      risk.readOnly = false;
      updateState({
        selectedRisks: updatedRisks,
        isEditingSavedData: true,
      });
    }
  };

  const handleRiskChange = (themeName, index, value) => {
    const themeRisks = state.selectedRisks[themeName] || [
      { risk_name: "", metrics: [], readOnly: false },
    ];
    const themeData = state.tData[0]?.themes.find((t) => t.name === themeName);
    const riskData = themeData?.risk_area.find(
      (ra) => ra.risk_name === value
    ) || {
      metrics: [],
    };
    themeRisks[index] = {
      ...themeRisks[index],
      risk_name: value,
      metrics: Array.isArray(riskData.metrics) ? riskData.metrics : [],
      readOnly: themeRisks[index].readOnly || false,
    };
    updateState({
      selectedRisks: { ...state.selectedRisks, [themeName]: themeRisks },
    });
  };

  const handleFieldChange = (themeName, index, field, value) => {
    const themeRisks = [
      ...(state.selectedRisks[themeName] || [
        { risk_name: "", metrics: [], readOnly: false },
      ]),
    ];
    themeRisks[index] = {
      ...themeRisks[index],
      [field]: field === "metrics" ? (value ? [value] : []) : value || "",
      readOnly: themeRisks[index].readOnly || false,
    };
    updateState({
      selectedRisks: { ...state.selectedRisks, [themeName]: themeRisks },
    });
  };

  const handleSearchChange = (event) =>
    updateState({ searchValue: event.target.value });

  const handleAiRecommendation = async () => {
    updateState({ loadingAi: true });
    try {
      const resources = Object.entries(state.selectedRisks)
        .flatMap(([themeName, risks]) =>
          risks.map((risk, index) => ({
            index,
            theme: themeName,
            risk_area: risk.risk_name,
            esg_metrics: risk.metrics[0] || "",
            readOnly: risk.readOnly,
          }))
        )
        .filter((risk) => risk.risk_area && !risk.readOnly);

      if (resources.length === 0) {
        updateState({ loadingAi: false });
        notifications.show({
          title: "No Data to Process",
          message: "Please add or edit risk areas to generate recommendations.",
          color: "yellow",
        });
        return;
      }

      const response = await AiApi.post("/process_request", {
        processor: "decision_sight_environmental",
        resources,
      });

      const updatedRisks = { ...state.selectedRisks };

      response.data.ai_response.forEach((res) => {
        const themeRisks = updatedRisks[res.theme];
        if (!themeRisks) return;

        const existingRiskIndex = themeRisks.findIndex(
          (risk) => risk.risk_name === res.risk_area
        );
        if (existingRiskIndex === -1) return;

        const risk = themeRisks[existingRiskIndex];
        if (!risk.readOnly) {
          risk.company_response =
            res.company_response || "Default company response";
          risk.recommendations = res.evaluation || "Default AI recommendation";
        }
      });

      updateState({ selectedRisks: updatedRisks, loadingAi: false });
      notifications.show({
        title: "AI Recommendation",
        message: "Recommendations updated successfully!",
        color: "blue",
      });
    } catch (error) {
      console.error("API error:", error);
      updateState({ loadingAi: false });
      notifications.show({
        title: "Error",
        message: "Failed to generate AI recommendations.",
        color: "red",
      });
    }
  };

  const sendData = async () => {
    updateState({ postLoading: true });
    try {
      const allRows = Object.entries(state.selectedRisks).flatMap(
        ([themeName, risks]) =>
          risks
            .filter((risk) => risk.risk_name)
            .map((risk) => ({
              id: risk.id || undefined,
              category:
                SCOPE_MAPPING[state.selectedScope] ||
                state.selectedScope.toLowerCase(),
              theme: themeName,
              risk_area: risk.risk_name,
              esg_metric: risk.metrics?.[0] || "",
              response: risk.company_response || "",
              ai_recommendation: risk.recommendations || "",
            }))
      );

      const newRows = allRows.filter((row) => !row.id);
      const updatedRows = allRows.filter((row) => row.id);

      if (allRows.length === 0) {
        updateState({ postLoading: false });
        notifications.show({
          title: "No Data",
          message: "No changes to save.",
          color: "yellow",
        });
        return;
      }

      if (newRows.length > 0) {
        console.log("Sending new rows to /add_ds_data:", newRows);
        await apiConfig.post("/add_ds_data", newRows);
      } else {
        console.log("No new rows to add.");
      }

      if (updatedRows.length > 0) {
        console.log("Sending updated rows to /update_ds_data:", updatedRows);
        await apiConfig.put("/update_ds_data", updatedRows);
      } else {
        console.log("No rows to update.");
      }

      const response = await apiConfig.get("/get_ds_data", {
        params: {
          category:
            SCOPE_MAPPING[state.selectedScope] ||
            state.selectedScope.toLowerCase(),
        },
      });

      const savedData = response.data;
      const formattedRisks = {};
      savedData.forEach((item) => {
        if (!formattedRisks[item.theme]) {
          formattedRisks[item.theme] = [];
        }
        formattedRisks[item.theme].push({
          risk_name: item.risk_area,
          metrics: [item.esg_metric],
          company_response: item.response,
          recommendations: item.ai_recommendation,
          readOnly: true,
          id: item.id,
        });
      });

      updateState({
        selectedRisks: formattedRisks,
        postLoading: false,
        isEditingSavedData: false,
      });

      notifications.show({
        title: "Success",
        message: "Data saved and updated successfully!",
        color: "green",
      });
    } catch (error) {
      console.error("Error sending data:", error);
      updateState({ postLoading: false });
      notifications.show({
        title: "Error",
        message: "Failed to save data. Please try again.",
        color: "red",
      });
    }
  };

  const handleExport = () => {
    const exportData = [];
    const pillar = state.selectedScope;

    state.tData[0]?.themes.forEach((theme) => {
      const themeRisks =
        state.selectedRisks[theme.name]?.length > 0
          ? state.selectedRisks[theme.name]
          : [
              {
                risk_name: "",
                metrics: [],
                company_response: "",
                recommendations: "",
                readOnly: false,
              },
            ];

      themeRisks.forEach((risk, index) => {
        const row = {
          Pillars: index === 0 ? pillar : "",
          Themes: index === 0 ? theme.name : "",
          "Risk Area": risk.risk_name || "",
          "ESG Metrics": risk.metrics?.[0] || "",
          "Company Response": risk.company_response || "",
          Recommendations: risk.recommendations || "",
        };
        exportData.push(row);
      });
    });

    const worksheet = XLSX.utils.json_to_sheet(exportData);

    worksheet["!cols"] = [
      { wch: 15 },
      { wch: 20 },
      { wch: 30 },
      { wch: 50 },
      { wch: 50 },
      { wch: 50 },
    ];

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, state.selectedScope);

    XLSX.writeFile(workbook, `${state.selectedScope}_Recommendations.xlsx`);

    notifications.show({
      title: "Export Successful",
      message: `Data for ${state.selectedScope} has been exported to Excel!`,
      color: "green",
    });
  };

  if (state.rerender) return <Loading />;

  const activeColumns =
    state.columnFilter.length > 0
      ? state.columnFilter
      : TABLE_HEADERS.map((h) => h.value);

  const rows = state.tData[0]?.themes
    .filter((theme) =>
      theme.name.toLowerCase().includes(state.searchValue.toLowerCase())
    )
    .flatMap((theme) => {
      const themeRisks =
        state.selectedRisks[theme.name]?.length > 0
          ? state.selectedRisks[theme.name]
          : [
              {
                risk_name: "",
                metrics: [],
                company_response: "",
                recommendations: "",
                readOnly: false,
              },
            ];
      return themeRisks.map((risk, index) => {
        const isSelected = state.selection.includes(theme.risk_area[0].id);
        const isFirstRiskArea = index === 0;
        const hasSavedData = risk.id !== undefined;

        return (
          <Table.Tr
            key={`${theme.name}-${index}`}
            className={isSelected ? "bg-[#07838F1A]" : ""}
          >
            {isFirstRiskArea && (
              <Table.Td rowSpan={themeRisks.length}>
                <Checkbox
                  checked={isSelected}
                  onChange={() => toggleRow(theme.risk_area[0].id, risk.id)}
                  color="#07838F"
                />
              </Table.Td>
            )}
            {activeColumns.includes("Themes") && isFirstRiskArea && (
              <Table.Td rowSpan={themeRisks.length}>
                <p className="text-left min-w-[200px]">{theme.name}</p>
              </Table.Td>
            )}
            {activeColumns.includes("Risk Area") && (
              <Table.Td>
                <ActionIcons
                  onAdd={
                    isFirstRiskArea ? () => handleAddRisk(theme.name) : null
                  }
                  onEdit={
                    hasSavedData ? () => handleEdit(theme.name, index) : null
                  }
                  onDelete={() => handleDeleteRow(theme.name, index)} // Updated to use the new handleDeleteRow
                  showEdit={hasSavedData}
                />
                <Select
                  placeholder="Select Risk Area"
                  className="w-[280px] mx-auto"
                  data={theme.risk_area.map((ra) => ra.risk_name)}
                  value={risk.risk_name || ""}
                  onChange={(val) => handleRiskChange(theme.name, index, val)}
                  disabled={risk.readOnly || false}
                />
              </Table.Td>
            )}
            {activeColumns.includes("ESG Metrics") && (
              <Table.Td>
                <ActionIcons onEdit={() => {}} />
                <Select
                  placeholder="Select ESG Metric"
                  className="w-[280px] mx-auto"
                  data={
                    Array.isArray(risk.metrics) && risk.metrics.length > 0
                      ? risk.metrics
                      : ["No metrics available"]
                  }
                  value={
                    Array.isArray(risk.metrics) && risk.metrics.length > 0
                      ? risk.metrics[0] || ""
                      : ""
                  }
                  onChange={(val) =>
                    val !== "No metrics available" &&
                    handleFieldChange(theme.name, index, "metrics", val)
                  }
                  disabled={
                    risk.readOnly ||
                    !risk.risk_name ||
                    !Array.isArray(risk.metrics) ||
                    risk.metrics.length === 0
                  }
                />
              </Table.Td>
            )}
            {activeColumns.includes("Company Response") && (
              <Table.Td>
                <TextareaGroup
                  placeholder="Enter company response"
                  value={risk.company_response}
                  onChange={(val) =>
                    handleFieldChange(
                      theme.name,
                      index,
                      "company_response",
                      val
                    )
                  }
                  disabled={risk.readOnly || false}
                />
              </Table.Td>
            )}
            {activeColumns.includes("AI Recommendation") && (
              <Table.Td>
                <ActionIcons onEdit={() => {}} />
                <TextareaGroup
                  placeholder="Enter recommendation"
                  value={risk.recommendations}
                  onChange={(val) =>
                    handleFieldChange(theme.name, index, "recommendations", val)
                  }
                  disabled={risk.readOnly || false}
                />
              </Table.Td>
            )}
          </Table.Tr>
        );
      });
    });

  return (
    <>
      <div className="flex justify-start gap-5 mb-8">
        {data.map((pillar) => (
          <Button
            key={pillar.Pillar}
            onClick={() => handleTabs(pillar.Pillar)}
            variant="filled"
            className={`${
              pillar.Pillar === state.selectedScope
                ? "text-primary hover:text-primary rounded-lg font-semibold bg-[#e6f3f4] hover:bg-[#e6f3f4] border border-primary"
                : "bg-white hover:bg-white text-black hover:text-black"
            }`}
          >
            {pillar.Pillar}
          </Button>
        ))}
      </div>
      <div className="p-2 my-1 shadow-lg grid items-center xl:justify-between px-4 bg-white rounded-lg w-full">
        <div className="col-span-3 grid items-start justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-6 sm:items-center w-full lg:gap-5">
          <TextInput
            className="w-full col-span-3"
            placeholder="Search by Topic Area or Assessment Question"
            rightSection={<CiSearch className="w-5 h-5" />}
            value={state.searchValue}
            onChange={handleSearchChange}
          />
          <div className="col-span-2 flex justify-center items-center gap-3 rounded-lg shadow-sm w-full relative z-10">
            <Button
              onClick={handleAiRecommendation}
              className="text-xs text-white bg-gradient-to-r from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] hover:bg-[#e6f3f4] hover:text-white rounded-lg w-full border-0"
              disabled={state.loadingAi}
            >
              {state.loadingAi ? (
                <Loading />
              ) : (
                <>
                  <RecommendationsDecisionSighStartIcon />
                  <span className="ms-2">{t("AI Recommendation")}</span>
                </>
              )}
            </Button>
            <Button
              onClick={handleExport}
              className="bg-[#e6f3f4] text-primary hover:bg-[#e6f3f4] hover:text-[#00C0A9] rounded-lg w-full"
            >
              {t("export")}
              <AiOutlineExport className="ms-2" />
            </Button>
          </div>
          <div className="col-span-1 m-3 bg-white hover:bg-white border-2 border-[#E8E7EA] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
            <Popover width={200} position="bottom" withArrow shadow="md">
              <Popover.Target>
                <TextInput
                  rightSection={<CiFilter />}
                  className="cursor-pointer"
                  readOnly
                  styles={{ input: { cursor: "pointer" } }}
                />
              </Popover.Target>
              <Popover.Dropdown>
                <ColumnChecklist
                  options={TABLE_HEADERS}
                  selected={state.columnFilter}
                  onChange={(val) => updateState({ columnFilter: val })}
                />
              </Popover.Dropdown>
            </Popover>
          </div>
        </div>
      </div>
      <div className="my-1 bg-white shadow-lg rounded-xl">
        <ScrollArea>
          <Table
            miw={1000}
            verticalSpacing="sm"
            className="bg-white shadow-lg"
            withColumnBorders
          >
            <Table.Thead className="pb-6 text-base font-thin bg-[#f5f4f5]">
              <Table.Tr className="text-black font-normal">
                <Table.Th style={{ width: rem(40) }}>
                  <Checkbox
                    onChange={toggleAll}
                    checked={state.selection.length > 0}
                    indeterminate={
                      state.selection.length > 0 &&
                      state.selection.length !==
                        state.tData[0]?.themes.flatMap((t) => t.risk_area)
                          .length
                    }
                    color="#07838F"
                  />
                </Table.Th>
                {activeColumns.includes("Themes") && (
                  <Table.Th>{t("Themes")}</Table.Th>
                )}
                {activeColumns.includes("Risk Area") && (
                  <Table.Th className="text-center">{t("Risk Area")}</Table.Th>
                )}
                {activeColumns.includes("ESG Metrics") && (
                  <Table.Th className="text-center">
                    {t("ESG Metrics")}
                  </Table.Th>
                )}
                {activeColumns.includes("Company Response") && (
                  <Table.Th className="text-center">
                    {t("Company Response")}
                  </Table.Th>
                )}
                {activeColumns.includes("AI Recommendation") && (
                  <Table.Th className="text-center">
                    {t("AI Recommendation")}
                  </Table.Th>
                )}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody className="text-base font-semibold text-gray-600">
              {rows}
            </Table.Tbody>
          </Table>
        </ScrollArea>
        <div className="flex mt-5 gap-5 items-center justify-end">
          <Button
            disabled={
              state.deleteLoading || state.selectedIdsForDelete.length === 0
            }
            className={`text-white ${
              state.deleteLoading || state.selectedIdsForDelete.length === 0
                ? "cursor-not-allowed opacity-50 bg-red-500"
                : "bg-red-500 hover:opacity-90"
            }`}
            onClick={handleDeleteSelected}
          >
            {state.deleteLoading ? <Loading /> : "Delete Selected"}
          </Button>
          <Button
            disabled={state.postLoading || isAllSolved}
            className={`text-white ${
              state.postLoading || isAllSolved
                ? "cursor-not-allowed opacity-50 bg-primary"
                : "bg-primary hover:opacity-90"
            }`}
            onClick={sendData}
          >
            {state.postLoading ? (
              <Loading />
            ) : state.isEditingSavedData ? (
              "Update"
            ) : (
              "Save"
            )}
          </Button>
        </div>
      </div>
    </>
  );
}
