import { Radar<PERSON>hart } from "@mantine/charts";
import { But<PERSON> } from "@mantine/core";
import React from "react";
import { useTranslation } from "react-i18next";
// import { RadarChart } from "recharts";

export default function LatestResults() {
  const { t } = useTranslation();
  const data = [
    {
      product: "Apples",
      sales: 120,
    },
    {
      product: "Oranges",
      sales: 98,
    },
    {
      product: "Tomatoes",
      sales: 86,
    },
    {
      product: "Grapes",
      sales: 99,
    },
    {
      product: "Bananas",
      sales: 85,
    },
    {
      product: "Lemons",
      sales: 65,
    },
  ];
  return (
    <>
      <div className="mt-5">
        <h1 className="text-xl font-bold">Latest Results</h1>
        <div className="flex justify-around p-5 mt-5 bg-transparent rounded-lg shadow-md outline outline-white">
          <Button className="bg-transparent text-primary hover:bg-transparent hover:text-primary">
            {t("Latest Results")}
          </Button>
          <Button className="bg-transparent text-[#9C9C9C] hover:bg-transparent hover:text-[#9C9C9C]">
            22 Aug, 2024
          </Button>
          <Button className="bg-transparent text-[#9C9C9C] hover:bg-transparent hover:text-[#9C9C9C]">
            21 Aug, 2024
          </Button>
          <Button className="bg-transparent text-[#9C9C9C] hover:bg-transparent hover:text-[#9C9C9C]">
            23 Aug, 2024
          </Button>
          <Button className="bg-transparent text-[#9C9C9C] hover:bg-transparent hover:text-[#9C9C9C]">
            24 Aug, 2024
          </Button>
          <Button className="bg-transparent text-[#9C9C9C] hover:bg-transparent hover:text-[#9C9C9C]">
            25 Aug, 2024
          </Button>
        </div>
        <div className="grid w-full grid-cols-5 pb-5 mt-5 bg-white rounded-lg shadow-md">
          <div className="">
            <RadarChart
              h={300}
              data={data}
              dataKey="product"
              series={[{ name: "sales", color: "teal", strokeColor: "" }]}
              withPolarAngleAxis={false}
              // withLegend
            />
            <p className="text-center">{t("Scenario")} no. 1</p>
          </div>
          <div className="">
            <RadarChart
              h={300}
              data={data}
              dataKey="product"
              series={[{ name: "sales", color: "teal", strokeColor: "" }]}
              withPolarAngleAxis={false}
              // withLegend
            />
            <p className="text-center">{t("Scenario")} no. 2</p>
          </div>
          <div className="">
            <RadarChart
              h={300}
              data={data}
              dataKey="product"
              series={[{ name: "sales", color: "teal", strokeColor: "" }]}
              withPolarAngleAxis={false}
              // withLegend
            />
            <p className="text-center">{t("Scenario")} no. 3</p>
          </div>
          <div className="">
            <RadarChart
              h={300}
              data={data}
              dataKey="product"
              series={[{ name: "sales", color: "teal", strokeColor: "" }]}
              withPolarAngleAxis={false}
              // withLegend
            />
            <p className="text-center">{t("Scenario")} no. 4</p>
          </div>
          <div className="">
            <RadarChart
              h={300}
              data={data}
              dataKey="product"
              series={[{ name: "sales", color: "teal", strokeColor: "" }]}
              withPolarAngleAxis={false}
              // withLegend
            />
            <p className="text-center">{t("Scenario")} no. 5</p>
          </div>
        </div>
      </div>
    </>
  );
}
