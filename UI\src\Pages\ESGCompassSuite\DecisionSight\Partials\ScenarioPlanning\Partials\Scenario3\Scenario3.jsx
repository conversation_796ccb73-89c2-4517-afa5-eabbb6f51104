import React from "react";
import { LineChart } from "@mantine/charts";

const Scenario3 = () => {
  const data = [
    {
      date: "Mar 22",
      Scope1: 2890,
      Scope2: 2338,
      Scope3: 2452,
    },
    {
      date: "Mar 23",
      Scope1: 2756,
      Scope2: 2103,
      Scope3: 2402,
    },
    {
      date: "Mar 24",
      Scope1: 3322,
      Scope2: 986,
      Scope3: 1821,
    },
    {
      date: "Mar 25",
      Scope1: 3470,
      Scope2: 2108,
      Scope3: 2809,
    },
    {
      date: "Mar 26",
      Scope1: 3129,
      Scope2: 1726,
      Scope3: 2290,
    },
  ];

  return (
    <div>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 rounded-xl bg-[#dfedf0] p-4 border-[#E8E7EA] border-2">
          ANALYSIS RESULTS Scenario 3
        </div>
        <div className="flex flex-row w-full justify-between p-4 items-center gap-4">
          <div className="w-full rounded-xl bg-[#dfedf0] p-4">
            <LineChart
              h={300}
              data={data}
              dataKey="date"
              series={[
                { name: "Scope1", color: "indigo.6" },
                { name: "Scope2", color: "blue.6" },
                { name: "Scope3", color: "teal.6" },
              ]}
              curveType="linear"
            />
          </div>
          {/* <div className="w-1/2">
            <BarChart
              h={300}
              data={data2}
              dataKey="month"
              orientation="vertical"
              yAxisProps={{ width: 80 }}
              barProps={{ radius: 10 }}
              series={[{ name: "Smartphones", color: "blue.6" }]}
            />
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default Scenario3;
