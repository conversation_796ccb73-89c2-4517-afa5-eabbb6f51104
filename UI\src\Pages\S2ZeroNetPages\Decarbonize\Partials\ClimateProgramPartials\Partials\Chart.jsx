import React from "react";
import {
  Area,
  CartesianGrid,
  ResponsiveContainer,
  Legend,
  XAxis,
  YAxis,
  <PERSON>ltip,
  AreaChart,
} from "recharts";

const Chart = ({ data, visibleSeries, onSwitchChange }) => {
  return (
    <div className="w-full rounded-3xl py-4 px-2 bg-white">
      <ResponsiveContainer width="100%" height={400}>
        <AreaChart
          data={data}
          dataKey="date"
          curveType="linear"
          withDots={false}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Legend />
          {visibleSeries.Scope1 && (
            <Area
              type="monotone"
              dataKey="Scope1"
              strokeDasharray="5 5"
              stroke="red"
              fill="red"
              strokeWidth={2}
            />
          )}
          {visibleSeries.Scope2 && (
            <Area
              type="monotone"
              dataKey="Scope2"
              strokeDasharray="5 5"
              stroke="blue"
              fill="blue"
              strokeWidth={2}
            />
          )}
          {visibleSeries.Scope3 && (
            <Area
              type="monotone"
              dataKey="Scope3"
              stroke="green"
              fill="green"
              strokeWidth={2}
            />
          )}
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default Chart;
