import React from "react";
import { Switch } from "@mantine/core";

export default function SPGraphkey(onSwitchChange) {
  return (
    <div className="flex md:ml-9 py-5 px-2 bg-white rounded-3xl">
      <div className="flex flex-col justify-between items-start gap-3">
        <h3 className="font-bold text-[#898989] mb-6">GRAPH KEY</h3>

        <div className="flex justify-center items-center gap-3 w-full">
          <span className="text-[#DA4848] font-bold mb-2 text-xl">...</span>
          <div className="flex flex-row justify-between mb-2 w-full">
            <span>Scenario no.1</span>
            <Switch
              onChange={() => onSwitchChange()}
              size="md"
              color="#07838F"
            />
          </div>
        </div>

        <div className="flex justify-center items-center gap-3 w-full">
          <span className="w-5 h-1 bg-[#29919B] mb-2"></span>
          <div className="flex justify-between mb-2 w-full">
            <span className="">Scenario no.2</span>
            <Switch
              onChange={() => onSwitchChange()}
              size="md"
              color="#07838F"
            />
          </div>
        </div>

        <div className="flex justify-center items-center gap-3 w-full">
          <span className="w-5 h-1 bg-[#E87E42]  mb-2"></span>
          <div className="flex justify-between mb-2 w-full">
            <span className="mr-20">Scenario no.3</span>
            <Switch
              onChange={() => onSwitchChange()}
              size="md"
              color="#07838F"
            />
          </div>
        </div>

        <div className="flex justify-center items-center gap-3 w-full">
          <span className="text-[#2DC9FA] mb-2 font-bold text-xl">...</span>
          <div className="flex justify-between mb-2 w-full">
            <span>Scenario no.4</span>
            <Switch size="md" color="#07838F" />
          </div>
        </div>

        <div className="flex justify-between items-center gap-3 w-full">
          <span className="text-[#DACB48] font-bold text-xl mb-2">...</span>
          <div className="flex justify-between w-full">
            <span>Scenario no.5</span>
            <Switch size="md" color="#07838F" />
          </div>
        </div>
      </div>
    </div>
  );
}
