import { useNavigate } from "react-router-dom";
import {
  ESGDangerIcon,
  ESGTaskIcon,
  ESGShieldIcon,
  ESGStatusUpIcon,
  ESGClipboardIcon,
  ESGShieldCheckIcon,
  ESGCircleRightArrow,
} from "@/assets/icons";
import S3Layout from "@/Layout/S3Layout";
import { IoMdHome } from "react-icons/io";
import { ModeToggle } from "../../../../../../Components/mode-toggle";


export default function ESGRiskManagementSystemMenu() {
  const navigate = useNavigate();

  const data = [
    {
      id: 1,
      name: "ESG Risk Universe and Governance",
      icon: <ESGDangerIcon active={true} />,
      groups: [
        {
          header: "Key Components",
          points: [
            "ESG Risk Taxonomy",
            "Assessment Matrix",
            "Roles & Responsibilities",
          ],
        },
        {
          header: "Functionalities",
          points: [
            "Comprehensive cataloging of ESG risks",
            "Impact and likelihood assessment",
            "Definition of roles and responsibilities",
          ],
        },
      ],
      path: "/green-shield/financial/ESG-risk-management/main/systems/1",
    },
    {
      id: 2,
      name: "IRO Identification & Assessment",
      icon: <ESGTaskIcon active={true} />,
      groups: [
        {
          header: "Key Components",
          points: [
            "Identification Tool",
            "Evaluation Engine",
            "Prioritization Mechanism",
          ],
        },
        {
          header: "Functionalities",
          points: [
            "Systematic risk identification process",
            "Quantitative and qualitative risk assessment",
            "Risk ranking & prioritisation, mitigation & ownership",
          ],
        },
      ],
      path: "/green-shield/financial/ESG-risk-management/main/systems/2",
    },
    {
      id: 3,
      name: "Mitigation Strategies",
      icon: <ESGShieldIcon active={true}/>,
      groups: [
        {
          header: "Key Components",
          points: ["Strategy Library", "Control Effectiveness"],
        },
        {
          header: "Functionalities",
          points: [
            "Catalog of ESG risk mitigation strategies",
            "Evaluation of control effectiveness",
          ],
        },
      ],
      path: "/green-shield/financial/ESG-risk-management/main/systems/3",
    },
    {
      id: 4,
      name: "Analytics & Reporting",
      icon: <ESGStatusUpIcon active={true}/>,
      groups: [
        {
          header: "Key Components",
          points: [
            "Executive Dashboard",
            "Stress Testing",
            "Scenario Analysis",
          ],
        },
        {
          header: "Functionalities",
          points: [
            "High-level reporting for decision-makers",
            "ESG risk stress testing capabilities",
            "Modeling of various ESG future scenarios",
          ],
        },
      ],
      path: "/green-shield/financial/ESG-risk-management/main/systems/4",
    },
    {
      id: 5,
      name: "Audit",
      icon: <ESGClipboardIcon active={true}/>,
      path: "/green-shield/financial/ESG-risk-management/main/systems/5",
    },
    {
      id: 6,
      name: "Anti-Greenwashing",
      icon: <ESGShieldCheckIcon active={true}/>,
      groups: [
        {
          header: "Key Components",
          points: ["Risk Assessment", "Mitigation Strategies"],
        },
        {
          header: "Functionalities",
          points: [
            "Identification of potential greenwashing risks",
            "Strategies to mitigate greenwashing",
          ],
        },
      ],
      path: "/green-shield/financial/ESG-risk-management/main/systems/6",
    },
  ];

  return (
    <S3Layout navbarTitle="ESG Integrated Risk & Impact Management"
    breadcrumbItems={[
      { title: <IoMdHome size={20}/>, href: "/get-started" },
      { title: "Risk & Impact Management", href: "#" },
    ]}>
            {/* <div className="bg-white text-black dark:bg-black dark:text-white min-h-screen p-4">
   <div className="flex justify-end mb-4">
    <ModeToggle />
  </div> */}


      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
 
        {data.map((system) => (
          <div
            key={system.id}
            className="flex flex-col justify-between   bg-white dark:bg-[#282828] text-gray-700 dark:text-white  rounded-xl mb-2 w-[100%] py-6 px-5 gap-4 overflow-hidden relative group cursor-pointer"
            onClick={() => navigate(system.path)}
          >
            <div className=" absolute bg-[#07838F1A] dark:bg-[#282828] dark:text-white w-[3rem] h-[3rem] left-[0.90rem] top-[1.2rem] rounded-[100%] group-hover:w-[80rem] group-hover:h-[50rem] group-hover:left-[-10rem] group-hover:top-[-10rem]  duration-1000  "></div>
            <div className="w-full h-full p-1 ">{system.icon}</div>
            <div className="flex justify-between items-center ">
              <h2 className=" font-semibold text-[#494949]   dark:text-white text-3xl text-left">
                {system.name}
              </h2>
              <div className="text-[#07838F] text-6xl">
                <ESGCircleRightArrow />
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* </div> */}
    </S3Layout>
  );
}
