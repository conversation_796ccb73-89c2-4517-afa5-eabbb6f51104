import MainLayout from "@/Layout/MainLayout";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import DSDashboard from "./Partials/DSDashboard/DSDashboard";
import DSRecommendations from "./Partials/DSRecommendations/DSRecommendations";
import ScenarioPlanningView from "./Partials/ScenarioPlanning/ScenarioPlanningView";
import { IoMdHome } from "react-icons/io";

export default function DecisionSight() {
  const { t } = useTranslation();
  const [active, setActive] = useState("Scenario");

  return (
    <>
      <MainLayout
        navbarTitle={"DecisionSight"}
        breadcrumbItems={[
          { title: <IoMdHome size={20} />, href: "/get-started" },
          { title: "DecisionSight", href: "#" },
        ]}
      >
        <div className="grid lg:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
          {/* <button
            className={`relative text-lg font-semibold py-3 px-6 transition-all ${
              active === "Dashboard"
                ? "active-tab rounded"
                : "text-[#5A5A5A] rounded-lg border-2"
            }`}
            onClick={() => setActive("Dashboard")}
          >
            {t("Dashboard")}
          </button> */}
          <button
            className={`relative text-lg font-semibold py-3 px-6 transition-all ${
              active === "Scenario"
                ? "active-tab rounded"
                : "text-[#5A5A5A] rounded-lg border-2"
            }`}
            onClick={() => setActive("Scenario")}
          >
            {t("Dashboard")}
          </button>
          <button
            className={`relative text-lg font-semibold py-3 px-6 transition-all ${
              active === "Recommendations"
                ? "active-tab rounded"
                : "text-[#5A5A5A] rounded-lg border-2"
            }`}
            onClick={() => setActive("Recommendations")}
          >
            {t("Recommendations")}
          </button>
        </div>

        {/* {active == "Dashboard" && <DSDashboard />} */}
        {active == "Recommendations" && <DSRecommendations />}
        {active == "Scenario" && <ScenarioPlanningView />}
      </MainLayout>
    </>
  );
}
