import { ArrowLeftIcon } from "@/assets/icons";
import { useEffect, useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";

import { SlMenu } from "react-icons/sl";
import { IoIosArrowBack } from "react-icons/io";
import SideBarLink from "./SideBarLink.jsx";
import {
  ChatBotIcon,
  LaunchpadIcon,
  LogOutIcons,
  OpenIcon,
  SettingsIcon,
  SupportIcon,
} from "@/assets/icons/MainSideBarIcons.jsx";
import useSideBarRoute from "@/hooks/useSideBarRoute.jsx";
import { useAuth } from "@/Contexts/AuthContext.jsx";
import { Tooltip } from "@mantine/core";

const Sidebar = ({ isOpen, setIsOpen,openChatBot }) => {
  const navigate = useNavigate();
  const { GetStart, adminPages } = useSideBarRoute();
  const { logout, superAdmin } = useAuth();

  const { pathname } = useLocation();
  const [mainTitle, setMainTitle] = useState("");
  const [toggleSideBar, setToggleSideBar] = useState(true);
  const [sideOpen, setSideOpen] = useState(false);
  const [subMenu, setSubMenu] = useState({});
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const handleMenu = (menu) => {
    if(menu.menuTitle == 'Admin Pages') {
    navigate('/admin-pages/manage-company');
    }
    setSubMenu(menu);
    setMainTitle(menu.menuTitle);
    if (!toggleSideBar) {
      setToggleSideBar(!toggleSideBar);
    }
  };

  
  useEffect(() => {
    setToggleSideBar(true);
    const menu = GetStart.find((menu) =>
      menu.menuLinks.some((link) => pathname.includes(link.url))
    );

    setSubMenu(menu);

    if (menu) {
      setMainTitle(menu.menuTitle);
    }
    if (pathname == "/get-started") {
      setMainTitle("Launchpad");
      setToggleSideBar(false);
    }
    if (pathname.includes('/Settings')) {
      setMainTitle("Settings");
      setToggleSideBar(false);
    }
    if (pathname == "/support") {
      setMainTitle("Support");
      setToggleSideBar(false);
    }

    if(pathname.includes('/admin-pages')){
      const menu = adminPages.find((menu) =>
        menu.menuLinks.some((link) => pathname.includes(link.url))
      );
      if(menu) {
        setSubMenu(menu);
        setMainTitle(menu.menuTitle);
      }
    }
  }, [pathname]);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 1024) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const hideSideMenu = () => {
    if(
      subMenu == undefined && pathname == "/get-started" || subMenu == undefined &&  pathname.includes('/Settings') || subMenu == undefined &&  pathname.includes('/support')
    ){
      return 'hidden';
    }else {
      return '';
    }
  }
  



  return (
    <div className="relative">
      <button
        className={`fixed top-[70px] transition-transform duration-300 ease-in-out ${
          isMobileMenuOpen ? "translate-x-[275px]" : "translate-x-4"
        } lg:hidden z-[60] bg-white dark:bg-[#1E1E1E] dark:text-white p-2 rounded-lg shadow-lg hover:bg-gray-50 transition-colors`}
        onClick={toggleMobileMenu}
      >
        {isMobileMenuOpen ? (
          <IoIosArrowBack className="text-[#07838F] text-xl" />
        ) : (
          <SlMenu className="text-[#07838F] text-xl" />
        )}
      </button>

      <div className="flex h-screen bg-white lg:relative absolute z-40">
        {/* Mobile Overlay */}
        {isMobileMenuOpen && (
          <div
            className="fixed inset-0 bg-black/50 lg:hidden z-[55]"
            onClick={toggleMobileMenu}
          />
        )}

        {/* Main Sidebar Container */}
        <div
          className={`
          flex h-screen
          ${
            isMobileMenuOpen
              ? "translate-x-0"
              : "-translate-x-full lg:translate-x-0"
          }
          transition-transform duration-300 ease-in-out
          fixed lg:relative
          bg-white
          dark:bg-[#1E1E1E] dark:text-white
          z-[56]
        `}
        >
          <div
            className={`h-full box-border  dark:border-[#292929] transition-all duration-300 border-r-2 bg-white overflow-auto scrollbar-hide flex dark:bg-[#1E1E1E] text-gray-900 dark:text-white`}
          >
            {/* logo and main side bar */}
            <div className="flex flex-col h-full rounded-t-lg px-2">
              <div className="flex items-center justify-between gap-2">
                {isOpen && (
                  <button
                    className="text-start text-[#07838F] rounded hover:text-[#07838F] hover:-translate-x-3 duration-300 py-1 px-3 sm:hidden"
                    onClick={() => setIsOpen(false)}
                  >
                    <ArrowLeftIcon />
                  </button>
                )}
              </div>
              <div className="mt-5 flex justify-between flex-col h-screen overflow-auto w-full scrollbar-hide">
                {/* main sidebar */}
                <div className="flex gap-6 flex-col items-center w-full flex-1">
                  <Link
                    className={`transition-all last:mb-2 ${
                      pathname === "/get-started"
                        ? "rounded-lg bg-[#e7f3f4]"
                        : ""
                    } px-5 py-1 hover:bg-[#e7f3f4] hover:rounded-lg`}
                    to={`/get-started`}
                    title="LunchPad"
                  >
                    <LaunchpadIcon />
                  </Link>
                  {GetStart.map((menu, i) => {
                    const isMenuActive = menu.menuLinks.some((link) =>
                      pathname.includes(link.url)
                    );

                    return (
                      <Tooltip key={i} label={menu.menuTitle}>
                        <button
                          onClick={() => handleMenu(menu)}
                          className={`transition-all last:mb-2 ${
                            isMenuActive ? "rounded-lg bg-[#e7f3f4] dark:bg-[#1E1E1E] " : ""
                          } px-5 py-1 hover:bg-[#e7f3f4] hover:rounded-lg`}
                        >
                          {menu.systemIcon}
                        </button>
                      </Tooltip>
                    );
                  })}
                  <Tooltip label={'Chat'}>
                    <button onClick={openChatBot}>
                    <ChatBotIcon />
                  </button>
                  </Tooltip>
                </div>
                <div className="mt-5 flex gap-2 flex-col items-center pb-20">
                  {superAdmin ? (
                    <>
                      {adminPages.map((menu, i) => {
                        const isMenuActive = menu.menuLinks.some((link) =>
                          pathname.includes(link.url)
                        );

                        return (
                          <Tooltip key={i} label={menu.menuTitle}>
                            <button
                              // title={menu.menuTitle}
                              onClick={() => handleMenu(menu)}
                              className={`transition-all last:mb-2 ${
                                isMenuActive ? "rounded-lg bg-[#e7f3f4]dark:bg-[#1E1E1E] " : ""
                              } px-5 py-1 hover:bg-[#e7f3f4] hover:rounded-lg`}
                            >
                              {menu.systemIcon}
                            </button>
                          </Tooltip>
                        );
                      })}
                    </>
                  ) : (
                    ""
                  )}
                  <Link
                    className={`transition-all last:mb-2 ${
                      pathname === "/Settings" ? "rounded-lg bg-[#e7f3f4] dark:bg-[#1E1E1E] dark:text-white" : ""
                    } px-5 py-1 hover:bg-[#e7f3f4] hover:rounded-lg`}
                    to={`/Settings`}
                    title="Settings"
                  >
                    <SettingsIcon />
                  </Link>

                  <Link
                    className={`transition-all last:mb-2 ${
                      pathname === "/support" ? "rounded-lg bg-[#e7f3f4] dark:bg-[#1E1E1E] dark:text-white" : ""
                    } px-5 py-1 hover:bg-[#e7f3f4] hover:rounded-lg`}
                    to={`/support`}
                    title="Support"
                  >
                    <SupportIcon />
                  </Link>

                  <button
                    onClick={logout}
                    title="Log out"
                    className={`transition-all last:mb-2 px-5 py-1 hover:bg-[#e7f3f4] hover:rounded-lg`}
                  >
                    <LogOutIcons />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Sub Sidebar */}
          <div
            className={`pt-20 
            ${
              hideSideMenu()
            }
            
            ${
              toggleSideBar ? "w-48 bg-white  dark:bg-[#1E1E1E] dark:text-white border-r-2 " : "w-9 bg-[#07838F1A] dark:bg-[#282828] dark:text-white"
            } transition-all duration-300`}
          >
            <div className="h-full w-full px-2">
              <div
                className={`flex items-center ${
                  mainTitle && subMenu?.menuTitle
                    ? "justify-between mb-4"
                    : "flex-col-reverse gap-28"
                } w-full`}
              >
                <h5
                  className={`font-bold text-[#676767]  dark:text-white  ${
                    mainTitle && subMenu?.menuTitle
                      ? ""
                      : "-rotate-90 whitespace-nowrap"
                  }`}
                >
                  {mainTitle}
                </h5>
                {toggleSideBar ? (
                  <button
                    className="rotate-180 "
                    title="Close Menu"
                    onClick={() => {
                      setSubMenu({});
                      setToggleSideBar(!toggleSideBar);
                      setSideOpen(!sideOpen);
                      const menu = GetStart.find((menu) =>
                        menu.menuLinks.some((link) =>
                          pathname.includes(link.url)
                        )
                      );

                      if (pathname == "/get-started") {
                        setMainTitle("Launchpad");
                        setSubMenu(undefined)
                      } else if (pathname == "/dashboard/notifications") {
                        setMainTitle("User Dashboard");
                      } else {
                        if (menu) {
                          setMainTitle(menu.menuTitle);
                        }
                      }
                      if(pathname.includes('Settings')){
                        
                        setMainTitle('Settings');
                      }
                    }}
                  >
                    <OpenIcon />
                  </button>
                ) : (
                  <button
                    title="Open Menu"
                    className="mt-2"
                    onClick={() => {
                      setToggleSideBar(!toggleSideBar);
                      setSideOpen(!sideOpen);
                      const menu = GetStart.find((menu) =>
                        menu.menuLinks.some((link) =>
                          pathname.includes(link.url)
                        )
                      );
                      setMainTitle("");
                      if (menu) {
                        setMainTitle(menu.menuTitle);
                        setSubMenu(menu);
                      }
                      if(pathname =='/AdminPages'){
                        const menu = adminPages.find((menu) =>
                          menu.menuLinks.some((link) => pathname.includes(link.url))
                        );
                        setSubMenu(menu);
                        setMainTitle(menu.menuTitle);
                      }
                    }}
                  >
                    <OpenIcon />
                  </button>
                )}
              </div>
              {subMenu?.menuLinks?.map((link, index) => (
                <SideBarLink {...link} key={index} />
              ))}
              
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
