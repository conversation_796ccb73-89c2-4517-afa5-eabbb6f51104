import React from "react";
import {
  ComposedChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Default<PERSON><PERSON>nd<PERSON>ontent,
  ResponsiveContainer,
} from "recharts";

const data = [
  { name: "2024", a: [40000, 1000] },
  { name: "2025", a: [42000, 2000] },
  { name: "2026", a: [44000, 30000] },
  { name: "2027", a: [46000, 40000] },
  { name: "2028", a: [48000, 50000] },
  { name: "2029", a: [50000, 55000] },
  { name: "2030", a: [52000, 60000] },
  { name: "2031", a: [54000, 62000] },
  { name: "2032", a: [56000, 63000] },
  { name: "2033", a: [58000, 64000] },
  { name: "2034", a: [60000, 60000] },
  { name: "2035", a: [60000, 60000] },
  { name: "2036", a: [60000, 60000] },
  { name: "2037", a: [60000, 60000] },
  { name: "2038", a: [60000, 60000] },
  { name: "2039", a: [60000, 60000] },
  { name: "2040", a: [60000, 60000] },
  { name: "2041", a: [60000, 60000] },
  { name: "2042", a: [60000, 60000] },
  { name: "2043", a: [60000, 60000] },
  { name: "2044", a: [60000, 60000] },
  { name: "2045", a: [60000, 60000] },
  { name: "2046", a: [60000, 60000] },
  { name: "2047", a: [60000, 60000] },
  { name: "2048", a: [60000, 60000] },
  { name: "2049", a: [60000, 60000] },
  { name: "2050", a: [60000, 60000] },
];
const Chart = (visibleSeries) => {
  const renderTooltipWithoutRange = ({ payload, content, ...rest }) => {
    const newPayload = payload.filter((x) => x.dataKey !== "a");
    return <Tooltip payload={newPayload} {...rest} />;
  };

  const renderLegendWithoutRange = ({ payload, content, ...rest }) => {
    const newPayload = payload.filter((x) => x.dataKey !== "a");
    return <DefaultLegendContent payload={newPayload} {...rest} />;
  };

  return (
    <div className="w-full rounded-3xl py-4 px-2 bg-white">
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis
            domain={[10000, "dataMax + 5000"]} //  تحديث نطاق المحور Y
            ticks={[0, 10000, 20000, 30000, 40000, 50000, 60000]} // القيم الظاهرة على المحور Y
          />
          <Tooltip content={renderTooltipWithoutRange} />
          <Area
            type="monotone"
            dataKey="a"
            stroke="none"
            fill="#aea3ba"
            connectNulls
            dot={false}
            activeDot={false}
          />
          <Legend content={renderLegendWithoutRange} />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default Chart;
