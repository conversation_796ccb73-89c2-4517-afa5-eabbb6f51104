import React from "react";
import { LineChart } from "@mantine/charts";

const Scenario5 = () => {
  const biaxialData = [
    { name: "2018", uv: 4000, pv: 2400 },
    { name: "2019", uv: 3000, pv: 1398 },
    { name: "2020", uv: 2000, pv: 9800 },
    { name: "2021", uv: 2780, pv: 3908 },
    { name: "2022", uv: 1890, pv: 4800 },
    { name: "2023", uv: 2390, pv: 3800 },
    { name: "2024", uv: 3490, pv: 4300 },
  ];
  return (
    <div>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 rounded-xl bg-[#dfedf0] p-4 border-[#E8E7EA] border-2">
          ANALYSIS RESULTS Scenario 5
        </div>
        <div className="flex flex-row w-full justify-between p-4 items-center gap-4">
          <div className="w-full bg-[#dfedf0] p-4 rounded-xl">
            <LineChart
              h={500}
              data={biaxialData}
              dataKey="name"
              series={[
                { name: "uv", color: "pink.6" },
                { name: "pv", color: "cyan.6", yAxisId: "right" },
              ]}
              curveType="natural"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Scenario5;