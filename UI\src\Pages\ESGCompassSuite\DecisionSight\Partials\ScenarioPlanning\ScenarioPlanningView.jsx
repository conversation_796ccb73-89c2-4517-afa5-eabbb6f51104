// import useSideBarRoute from "@/hooks/useSideBarRoute";
// import S2Layout from "@/Layout/S3Layout";
import { Tabs } from "@mantine/core";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import Scenario1 from "./Partials/Scenario1/Scenario1";
import Scenario2 from "./Partials/Scenario2/Scenario2";
import Scenario3 from "./Partials/Scenario3/Scenario3";
import Scenario4 from "./Partials/Scenario4/Scenario4";
import Scenario5 from "./Partials/Scenario5/Scenario5";
import DSDashboard from "../../Partials/DSDashboard/DSDashboard";

// import  {
//     ProfileAnalytics,
//     ListedEquity,
//     ProjectFinance,
//     AutoLoans,
//     BusinessLoans,
// } from "@/assets/svg/ImageSVG.jsx";

const ScenarioPlanningView = () => {
  // const { netZeroMenu } = useSideBarRoute();
  const { t } = useTranslation();

  const [activeTab, setActiveTab] = useState("Scenario1");
  return (
    <div data-navbar-title="Scenario Planning">
      {/* <p className="-mt-8 mb-6 text-[#8F8F8F] text-sm">Scenario planning from 1 - 5</p> */}
      <div className="pb-8">
        <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
          <Tabs.List className="flex flex-row justify-center gap-2 p-2 mb-6 rounded-xl shadow-lg md:justify-around md:flex-row before:hidden bg-white ">
            <Tabs.Tab
              value="Scenario1"
              className={`flex flex-row justify-start items-center text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "Scenario1"
                  ? "text-primary bg-[#E6F3F4]"
                  : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
              <div className="flex flex-row items-center space-x-2">
                {/* <ProfileAnalytics color={activeTab === "Scenario1" ? "#07838F" : "#8F8F8F"}/>  */}
                <span>{t("Scenario 1")}</span>
              </div>
            </Tabs.Tab>
            <Tabs.Tab
              value="Scenario2"
              className={`flex flex-row justify-start items-center text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "Scenario2"
                  ? "text-primary bg-[#E6F3F4]"
                  : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
              <div className="flex flex-row items-center space-x-2">
                {/* <ProfileAnalytics color={activeTab === "Scenario1" ? "#07838F" : "#8F8F8F"}/>  */}
                <span>{t("Scenario 2")}</span>
              </div>
            </Tabs.Tab>

            <Tabs.Tab
              value="Scenario3"
              className={`border-none outline-none rounded-lg font-normal hover:bg-transparent ${
                activeTab === "Scenario3"
                  ? "text-primary bg-[#E6F3F4]"
                  : "text-[#8F8F8F] bg-[#ffffff]"
              }`}
            >
              <div className="flex flex-row items-center space-x-2">
                {/* <ListedEquity color={activeTab === "Scenario2" ? "#07838F" : "#8F8F8F"} /> */}
                <span>{t("Scenario 3")}</span>
              </div>
            </Tabs.Tab>

            <Tabs.Tab
              value="Scenario4"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "Scenario4"
                  ? "text-primary bg-[#E6F3F4]"
                  : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
              <div className="flex flex-row items-center space-x-2">
                {/* <ProjectFinance color={activeTab === "Scenario3" ? "#07838F" : "#8F8F8F"} /> */}
                <span>{t("Scenario 4")}</span>
              </div>
            </Tabs.Tab>

            <Tabs.Tab
              value="Scenario5"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "Scenario5"
                  ? "text-primary bg-[#E6F3F4]"
                  : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
              <div className="flex flex-row items-center space-x-2">
                {/* <AutoLoans color={activeTab === "Scenario4" ? "#07838F" : "#8F8F8F"} /> */}
                <span>{t("Scenario 5")}</span>
              </div>
            </Tabs.Tab>

            <Tabs.Tab
              value="Scenario6"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "Scenario6"
                  ? "text-primary bg-[#E6F3F4]"
                  : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
              <div className="flex flex-row items-center space-x-2">
                {/* <BusinessLoans color={activeTab === "Scenario5" ? "#07838F" : "#8F8F8F"} /> */}
                <span>{t("Scenario 6")}</span>
              </div>
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="Scenario1">
            <DSDashboard />
          </Tabs.Panel>
          <Tabs.Panel value="Scenario2">
            <Scenario1 />
          </Tabs.Panel>

          <Tabs.Panel value="Scenario3">
            <Scenario2 />
          </Tabs.Panel>

          <Tabs.Panel value="Scenario4">
            <Scenario3 />
          </Tabs.Panel>

          <Tabs.Panel value="Scenario5">
            <Scenario4 />
          </Tabs.Panel>

          <Tabs.Panel value="Scenario6">
            <Scenario5 />
          </Tabs.Panel>
        </Tabs>
      </div>
    </div>
  );
};

export default ScenarioPlanningView;
